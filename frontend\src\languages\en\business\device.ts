export default {
  configure: {
    remoteSet: "Remote Set"
  },
  console: {
    title: "Console",
    clear: "Clear",
    selectAll: "Select All",
    copy: "Copy",
    copySuccess: "Copy successful",
    noTextSelected: "No text selected",
    copyFailed: "Copy failed",
    clearSuccess: "Console cleared",
    collapse: "Collapse",
    expand: "Expand"
  },
  groupInfo: {
    title: "Group Info",
    table: {
      id: "Index",
      name: "Name",
      desc: "Description",
      fc: "FC",
      count: "Count"
    },
    messages: {
      fetchDataError: "Error occurred while fetching data",
      fetchedData: "Fetched data:"
    }
  },
  treeClickLog: "treeClick clicked : ",
  contentView: "Content View",
  emptyDeviceId: "Current device id is empty",
  invalidResponseStructure: "Invalid response structure",
  formattedMenuDataLog: "Formatted menu data ===",
  allSettings: "All Settings",
  allEditSpSettings: "All Single Area Settings",
  allEditSgSettings: "All Multi Area Settings",
  deviceTreeDataLog: "Device tree data",
  failedToLoadMenu: "Failed to load device menu:",
  innerTabs: {
    contentView: "Content",
    fileUpload: "Upload",
    fileDownload: "Download",
    deviceTime: "Time Sync",
    deviceOperate: "Operation",
    variableDebug: "Debug",
    oneClickBackup: "Backup",
    entryConfig: "Config",
    tabClickLog: "Tab clicked:"
  },
  devices: {
    notConnectedAlt: "Device not connected",
    pleaseConnect: "Please connect the device first!"
  },
  list: {
    unnamedDevice: "Unnamed Device",
    connected: "Connected",
    disconnected: "Disconnected",
    connect: "Connect",
    edit: "Edit",
    disconnect: "Disconnect",
    remove: "Delete",
    noDeviceFound: "No device found",
    handleClickLog: "handleListClick clicked:",
    disconnectBeforeEdit: "Please disconnect before editing",
    connectSuccess: "Device {name}: Connected successfully",
    connectExist: "Device {name}: Connection already exists",
    connectFailed: "Device {name}: Connection failed",
    connectFailedReason: "Device connection failed reason:",
    disconnectedSuccess: "Device {name}: Disconnected",
    operateFailed: "Device {name}: Operation failed",
    disconnectBeforeDelete: "Please disconnect before deleting",
    dataLog: "Data:",
    ipPortExist: "This IP and port already exist, please do not add again"
  },
  search: {
    placeholder: "Search device",
    ipPortExist: "This IP and port already exist, please do not add again"
  },
  summaryPie: {
    other: "Other",
    title: "Setting Count Ratio",
    subtext: "Setting Group Value"
  },
  deviceInfo: {
    title: "Device Info",
    export: "Export",
    exportTitle: "Export Device Info",
    exportLoading: "Exporting device basic info...",
    exportSuccess: "Device basic info exported successfully",
    exportFailed: "Failed to export device basic info",
    getInfoFailed: "Failed to get device info. Reason: {msg}",
    getInfoFailedEmpty: "Failed to get device info. Reason: Data is empty!",
    defaultFileName: "Device Info.xlsx",
    confirm: "Confirm",
    tip: "Tip"
  },
  allParamSetting: {
    title: "All Settings",
    autoRefresh: "Auto Refresh",
    refresh: "Refresh",
    confirm: "Confirm",
    import: "Import",
    export: "Export",
    groupTitle: "Setting Group:",
    allGroups: "All",
    noDataToImport: "No data to import",
    importSuccess: "Settings imported successfully",
    importFailed: "Failed to import settings: {msg}",
    requestFailed: "Request failed, please try again later",
    queryFailed: "Failed to query settings: {msg}",
    unsavedChanges: "There are unsaved changes, continue to refresh?",
    confirmButton: "Confirm",
    cancelButton: "Cancel",
    alertTitle: "Tip",
    errorTitle: "Error",
    noDataToConfirm: "No data to confirm",
    confirmSuccess: "Settings updated successfully",
    confirmFailed: "Failed to update settings: ",
    responseLog: "Response data:",
    continueAutoRefresh: "Continue to enable auto refresh",
    settingGroup: "Setting Group",
    all: "All",
    minValue: "Min Value",
    maxValue: "Max Value",
    step: "Step",
    unit: "Unit",
    searchNamePlaceholder: "Enter setting name to search",
    searchDescPlaceholder: "Enter setting description to search",
    autoRefreshWarning: "Auto refresh is enabled, modification is not allowed",
    invalidValue: "The value {value} of setting {name} is not within the valid range",
    exportFileName: "Device Parameter Settings_All Settings.xlsx",
    selectPathLog: "Select path: ",
    exportSuccess: "Export setting list successfully"
  },
  variable: {
    autoRefresh: "Auto Refresh",
    variableName: "Variable Name",
    inputVariableName: "Please enter the variable name to add",
    refresh: "Refresh",
    add: "Add",
    confirm: "Confirm",
    import: "Import",
    export: "Export",
    delete: "Delete",
    noDataToConfirm: "No data to confirm",
    warning: "Warning",
    variableModifiedSuccess: "Variable modified successfully",
    variableModifiedFailed: "Failed to modify variable, reason:",
    requestFailed: "Request failed, please try again later",
    error: "Error",
    success: "Success",
    variableAddSuccess: "Variable added successfully",
    variableAddFailed: "Failed to add variable, reason:",
    variableDeleteSuccess: "Variable deleted successfully",
    variableDeleteFailed: "Failed to delete variable, reason:",
    exportSuccess: "Device debug variable info exported successfully",
    exportFailed: "Failed to export device debug variable info, reason:",
    importSuccess: "Device debug variable info imported successfully",
    importFailed: "Failed to import device debug variable info:",
    confirmRefresh: "There are unsaved changes, continue to refresh?",
    confirmAutoRefresh: "There are unsaved changes, continue to enable auto refresh?",
    pleaseInputVariableName: "Please enter the variable name",
    exportTitle: "Export Device Debug Variable",
    importTitle: "Import Device Debug Variable",
    defaultExportPath: "Device Debug Variable.xlsx",
    title: "Variable Debug",
    variableNamePlaceholder: "Please enter the variable name to add",
    batchDelete: "Batch Delete",
    modifySuccess: "Variable modified successfully",
    modifyFailed: "Failed to modify variable, reason: {msg}",
    alertTitle: "Warning",
    successTitle: "Tip",
    errorTitle: "Error",
    confirmButton: "Confirm",
    cancelButton: "Cancel",
    sequence: "Index",
    name: "Name",
    value: "Value",
    type: "Type",
    description: "Description",
    address: "Address",
    operation: "Operation",
    enterVariableName: "Please enter the variable name to add",
    responseLog: "Response data:",
    addSuccess: "Variable added successfully",
    addFailed: "Failed to add variable, reason:",
    addFailedWithName: "Failed to add variable {name}: {reason}",
    exportFileName: "Device Debug Variable.xlsx",
    selectPathLog: "Select path:",
    exportSuccessLog: "Device debug variable info exported successfully, {path}",
    exportFailedLog: "Failed to export device debug variable info, reason:",
    importFailedLog: "Failed to import device debug variable info:",
    unsavedChanges: "There are unsaved changes, continue to refresh?",
    continueAutoRefresh: "Continue to enable auto refresh",
    tip: "Tip",
    sequenceNumber: "Index"
  },
  backup: {
    sequence: "Index",
    title: "Device Backup",
    savePath: "Save Path",
    setPath: "Set Backup Save Path",
    setPathTitle: "Set Path",
    startBackup: "Start Backup",
    cancelBackup: "Cancel Backup",
    backup: "Backup",
    backupType: "Backup Type",
    progress: "Progress",
    status: "Status",
    operation: "Operation",
    backupTypes: {
      paramValue: "Device Parameter Values",
      faultInfo: "Device Fault Reports",
      cidConfigPrjLog: "CID/CCD/Device Config/Debug Info/PRJ/Log",
      waveReport: "Device Wave Files"
    },
    backupDesc: "Backup Content Description",
    backupDescTypes: {
      paramValue: "Export device parameter values (param export.xlsx)",
      faultInfo: "Export device fault info (event/operation/fault/audit reports)",
      cidConfigPrjLog: "Export configuration files (CID/CCD, XML config, log files)",
      waveReport: "Export device wave files (/wave/comtrade)"
    },
    locateFolder: "Locate Folder",
    backupSuccess: "Backup Successful",
    openFolderFailed: "Failed to open folder",
    backupFailed: "Backup Failed",
    noTypeSelected: "Please select backup type first",
    cancelSuccess: "Cancel successful",
    cancelFailed: "Cancel failed"
  },
  operate: {
    title: "Device Operation",
    manualWave: "Manual Wave Recording",
    resetDevice: "Device Reset",
    clearReport: "Clear Report",
    clearWave: "Clear Wave Recording",
    executing: "Executing...",
    selectOperation: "Please select operation",
    success: {
      manualWave: "Manual Wave Recording successful",
      resetDevice: "Device Reset successful",
      clearReport: "Clear Report successful",
      clearWave: "Clear Wave Recording successful"
    },
    fail: {
      manualWave: "Failed to record manual wave, reason:",
      resetDevice: "Failed to reset device, reason:",
      clearReport: "Failed to clear report, reason:",
      clearWave: "Failed to clear wave recording, reason:"
    }
  },
  time: {
    title: "Device Time Sync",
    currentTime: "Current Time",
    deviceTime: "Device Time",
    selectDateTime: "Select Date and Time",
    milliseconds: "Milliseconds",
    now: "Now",
    read: "Read",
    write: "Write",
    readSuccess: "Device time read successfully.",
    readFailed: "Failed to read device time: {msg}",
    readFailedInvalidFormat: "Failed to read device time: Invalid time format",
    readFailedDataError: "Failed to read device time: Time data format error",
    writeSuccess: "Device time written successfully.",
    writeFailed: "Failed to write device time: {msg}",
    writeFailedInvalidFormat: "Failed to write device time: Invalid time format",
    millisecondsRangeError: "Milliseconds value range should be between 0-999",
    unknownError: "Unknown error"
  },
  reportOperate: {
    title: "Report Operation",
    date: "Date:",
    search: "Search",
    save: "Save",
    clearList: "Clear List",
    loading: "Data loading",
    progress: {
      title: "Progress Information",
      loading: "Loading",
      searching: "Searching {type}"
    },
    table: {
      reportId: "Report ID",
      name: "Name",
      time: "Time",
      operationAddress: "Operation Address",
      operationParam: "Operation Parameter",
      value: "Value",
      step: "Step",
      source: "Source",
      sourceType: "Source Type",
      result: "Result"
    },
    messages: {
      selectDateRange: "Please select complete date range",
      noDataToSave: "No data to save",
      saveSuccess: "Save successful",
      saveReport: "Save Report"
    }
  },
  reportGroup: {
    title: "Report Group",
    date: "Date:",
    search: "Search",
    save: "Save",
    clearList: "Clear List",
    autoRefresh: "Auto Refresh",
    loading: "Data loading",
    progress: {
      title: "Progress Information",
      loading: "Loading",
      searching: "Searching {type}"
    },
    table: {
      reportId: "Report ID",
      time: "Time",
      description: "Description"
    },
    contextMenu: {
      uploadWave: "Upload Wave",
      getHistoryReport: "Get History Report",
      saveResult: "Save Result",
      clearContent: "Clear Content"
    },
    messages: {
      selectDateRange: "Please select complete date range",
      noDataToSave: "No data to save",
      noFileToUpload: "No file to upload",
      saveSuccess: "Save successful",
      saveReport: "Save Report",
      waveToolNotConfigured: "Wave tool not configured",
      waveFileUploading: "Wave file uploading",
      waveFileUploadComplete: "Wave file upload complete",
      waveFileUploadCompleteWithPath: "Wave file upload complete, path: {path}",
      openWaveFileConfirm: "Open wave file with third-party tool?",
      openWaveFileTitle: "Reminder",
      confirm: "Confirm",
      cancel: "Cancel"
    },
    refresh: {
      stop: "Stop Refresh",
      start: "Auto Refresh"
    },
    hiddenItems: {
      show: "Show Hidden Items",
      hide: "Hide Hidden Items"
    }
  },
  fileDownload: {
    title: "File Download",
    deviceDirectory: "Device Directory",
    reboot: "Reboot",
    noReboot: "No Reboot",
    selectFile: "Select File",
    addDownloadFile: "Add Download File",
    addDownloadFolder: "Add Download Folder",
    addDownloadFilesAndFolders: "Add Files and Folders",
    downloadFile: "Download File",
    cancelDownload: "Cancel Download",
    importList: "Import List",
    exportList: "Export List",
    batchDelete: "Batch Delete",
    clearList: "Clear List",
    download: "Download",
    delete: "Delete",
    fileName: "File Name",
    fileSize: "File Size",
    filePath: "File Path",
    lastModified: "Last Modified",
    progress: "Progress",
    status: "Status",
    operation: "Operation",
    folder: "Folder",
    waitingDownload: "Waiting Download",
    calculatingFileInfo: "Calculating File Info",
    downloadPreparing: "Download Preparing",
    downloading: "Downloading......",
    downloadComplete: "Download Complete",
    downloadError: "Download Error:",
    userCancelled: "User Cancelled",
    allFilesComplete: "Download Complete",
    fileExists: "File {path} already exists, add failed!",
    selectValidFile: "Please select a valid file for download operation",
    remotePathEmpty: "Remote path cannot be empty",
    noDownloadTask: "No download task obtained, cannot cancel",
    downloadCancelled: "Download {path} cancelled complete",
    downloadCancelledFailed: "Download {path} cancelled failed, reason: {msg}",
    fileDeleted: "File {path} deleted complete",
    exportSuccess: "Export download file list successful",
    exportFailed: "Export download file list failed",
    importSuccess: "Import download file list successful",
    importFailed: "Import download file list failed: {msg}",
    downloadList: "Download File List",
    exportTitle: "Export Download File List",
    importTitle: "Import Download File List",
    error: "Error",
    tip: "Tip",
    confirm: "Confirm",
    sequence: "Index",
    confirmButton: "Confirm",
    cancelButton: "Cancel",
    alertTitle: "Tip",
    errorTitle: "Error",
    successTitle: "Success",
    warningTitle: "Warning",
    loading: "Loading",
    executing: "Executing...",
    noData: "No Data",
    selectDateRange: "Please select date range",
    search: "Search",
    save: "Save",
    clear: "Clear",
    refresh: "Refresh",
    stop: "Stop",
    start: "Start",
    show: "Show",
    hide: "Hide",
    showHiddenItems: "Show Hidden Items",
    hideHiddenItems: "Hide Items",
    continue: "Continue",
    cancel: "Cancel",
    confirmImport: "Confirm Import",
    confirmExport: "Confirm Export",
    confirmDelete: "Confirm Delete",
    confirmClear: "Confirm Clear",
    confirmCancel: "Confirm Cancel",
    confirmContinue: "Confirm Continue",
    confirmStop: "Confirm Stop",
    confirmStart: "Confirm Start",
    confirmShow: "Confirm Show",
    confirmHide: "Confirm Hide",
    confirmRefresh: "Confirm Refresh",
    confirmSave: "Confirm Save",
    confirmSearch: "Confirm Search",
    confirmClearList: "Confirm Clear List",
    confirmImportList: "Confirm Import List",
    confirmExportList: "Confirm Export List",
    confirmBatchDelete: "Confirm Batch Delete",
    confirmDownload: "Confirm Download",
    confirmCancelDownload: "Confirm Cancel Download",
    confirmDeleteFile: "Confirm Delete File",
    confirmClearFiles: "Confirm Clear Files",
    confirmImportFiles: "Confirm Import Files",
    confirmExportFiles: "Confirm Export Files",
    confirmBatchDeleteFiles: "Confirm Batch Delete Files",
    confirmDownloadFiles: "Confirm Download Files",
    confirmCancelDownloadFiles: "Confirm Cancel Download Files",
    rename: "Rename for Download",
    renamePlaceholder: "Rename when downloading (optional)",
    renameCopyFailed: "Failed to copy file for rename:",
    packageProgram: "Program Packaging",
    selectSaveDir: "Select Save Directory",
    packageBtn: "Package",
    locateDir: "Locate Folder",
    saveDirEmpty: "Please select a save directory first!",
    packageSuccess: "Program packaging completed!",
    packageFailed: "Packaging failed: {msg}",
    noFileSelected: "Please select the files to be packaged!",
    zipPath: "Zip Path: {zipPath}",
    addToDownload: "Add to Download",
    fileAdded: "File added to download list: {path}",
    rebootSuccess: "Device reboot successful",
    rebootFailed: "Device reboot failed: {msg}"
  },
  fileUpload: {
    serialNumber: "Index",
    title: "File Upload",
    importList: "Import List",
    exportList: "Export List",
    batchDelete: "Batch Delete",
    clearList: "Clear List",
    upload: "Upload",
    cancelUpload: "Cancel Upload",
    delete: "Delete",
    sequence: "Index",
    fileName: "File Name",
    fileSize: "File Size",
    filePath: "File Path",
    lastModified: "Last Modified",
    progress: "Progress",
    statusTitle: "Status",
    status: {
      waiting: "Waiting Upload",
      preparing: "Upload Preparing",
      uploading: "Uploading......",
      completed: "Upload Complete",
      error: "Upload Error:",
      cancelled: "User Cancelled"
    },
    operation: "Operation",
    calculatingFileInfo: "Calculating File Info",
    uploadPreparing: "Upload Preparing",
    uploading: "Uploading......",
    uploadComplete: "Upload Complete",
    uploadError: "Upload Error: {errorMsg}",
    userCancelled: "User Cancelled",
    allFilesComplete: "Upload Complete",
    fileExists: "File {path} already exists, add failed!",
    invalidFile: "Please select a valid file for upload operation",
    emptySavePath: "File save path cannot be empty",
    fileUploadComplete: "File {fileName} upload complete",
    selectPath: "Select Path",
    pathOptions: {
      shr: "/shr",
      configuration: "/shr/configuration",
      log: "/log",
      wave: "/wave",
      comtrade: "/wave/comtrade"
    },
    deviceDirectory: "Device Directory",
    savePath: "Save Path",
    setPath: "Set Path",
    getFiles: "Get Files",
    uploadFiles: "Upload Files",
    errors: {
      invalidFile: "Please select a valid file for upload operation",
      emptySavePath: "File save path cannot be empty",
      noUploadTask: "No upload task obtained, cannot cancel",
      getFilesFailed: "Failed to get device directory files"
    },
    messages: {
      uploadCompleted: "File upload complete",
      uploadCancelled: "File upload cancelled complete",
      clearListSuccess: "Clear file list successful"
    }
  },
  info: {
    title: "Device Info",
    export: "Export",
    exportSuccess: "Export device basic info successful",
    exportFailed: "Export device basic info failed",
    exportTip: "Tip",
    confirm: "Confirm",
    exportLoading: "Exporting device basic info...",
    getInfoFailed: "Failed to get device info. Reason: ",
    dataEmpty: "Data is empty!"
  },
  summary: {
    title: "Device Group Overview",
    basicInfo: "Basic Info",
    settingTotal: "Total Settings",
    telemetry: "Telemetry",
    teleindication: "Teleindication",
    telecontrol: "Telecontrol",
    driveOutput: "Drive Output",
    settingRatio: "Setting Ratio"
  },
  dict: {
    refresh: "Refresh",
    confirm: "Confirm",
    import: "Import",
    export: "Export",
    sequence: "Index",
    shortAddress: "Short Address",
    shortAddressTooltip: "Enter short address to search",
    chinese: "Chinese",
    english: "English",
    spanish: "Spanish",
    french: "French",
    operation: "Operation",
    confirmLog: "Confirm Dictionary",
    importLog: "Import Dictionary",
    exportLog: "Export Dictionary",
    refreshLog: "Refresh Dictionary",
    newValueLog: "New Value:"
  },
  allParamCompare: {
    title: "Import All Setting Value Difference Comparison",
    cancel: "Cancel",
    confirm: "Confirm Import",
    groupName: "Group Name",
    name: "Name",
    description: "Description",
    minValue: "Min Value",
    maxValue: "Max Value",
    step: "Step",
    unit: "Unit",
    address: "Address",
    oldValue: "Old Value",
    newValue: "New Value",
    sequence: "Index",
    searchName: "Enter setting name to search",
    searchDescription: "Enter setting description to search",
    messages: {
      noSelection: "No data selected",
      error: "Error"
    }
  },
  deviceForm: {
    title: {
      add: "Add Device",
      edit: "Edit Device"
    },
    name: "Device Name",
    ip: "IP Address",
    port: "Port",
    connectTimeout: "Connect Timeout (Milliseconds)",
    readTimeout: "Global Request Timeout (Milliseconds)",
    paramTimeout: "Setting Value Modify Timeout (Milliseconds)",
    encrypted: "Encrypted Connection",
    advanced: {
      show: "Expand Advanced Options",
      hide: "Hide Advanced Options"
    },
    buttons: {
      cancel: "Cancel",
      confirm: "Confirm"
    },
    messages: {
      nameRequired: "Please enter device name",
      nameTooLong: "Device name should not be too long",
      invalidIp: "Please enter a valid IP address",
      invalidPort: "Port must be between 1-65535",
      timeoutTooShort: "Timeout should not be too short"
    }
  },
  paramCompare: {
    title: "Import Setting Value Difference Comparison",
    cancel: "Cancel",
    confirm: "Confirm Import",
    sequence: "Index",
    name: "Name",
    description: "Description",
    minValue: "Min Value",
    maxValue: "Max Value",
    step: "Step",
    unit: "Unit",
    address: "Address",
    oldValue: "Old Value",
    newValue: "New Value",
    searchName: "Enter setting name to search",
    searchDescription: "Enter setting description to search",
    messages: {
      noSelection: "No data selected",
      error: "Error"
    }
  },
  progress: {
    title: "Progress Information",
    executing: "Executing..."
  },
  remoteYm: {
    title: "Remote Telemetry",
    sequence: "Index",
    shortAddress: "Short Address",
    description: "Description",
    value: "Value",
    operation: "Operation",
    inputShortAddressFilter: "Enter short address filter",
    inputDescriptionFilter: "Enter description filter",
    invalidData: "Data {name} value {value} is not valid",
    error: "Error",
    success: "Success",
    executeSuccess: "Execute successful",
    prompt: "Prompt",
    confirmButton: "Confirm",
    confirmExecute: "Confirm Execute",
    confirm: "Confirm",
    executeButton: "Execute",
    cancelButton: "Cancel"
  },
  remoteYt: {
    title: "Remote Telecontrol",
    sequence: "Index",
    directControl: "Direct Control",
    selectControl: "Select Control",
    shortAddress: "Short Address",
    description: "Description",
    value: "Value",
    operation: "Operation",
    inputShortAddressFilter: "Enter short address filter",
    inputDescriptionFilter: "Enter description filter",
    invalidData: "Data {name} value {value} is not valid",
    error: "Error",
    success: "Success",
    executeSuccess: "Execute successful",
    prompt: "Prompt",
    confirm: "Confirm",
    errorInfo: "Error Information",
    executeFailed: "Remote telecontrol execute failed, reason: {msg}",
    executeSuccessLog: "{desc} Remote telecontrol execute successful",
    cancelSuccess: "Cancel successful",
    cancelFailed: "Remote telecontrol cancel failed, reason: {msg}",
    selectSuccess: "Select successful, execute?",
    confirmInfo: "Confirm Information",
    execute: "Execute",
    cancel: "Cancel"
  },
  paramSetting: {
    title: "Device Setting Value",
    autoRefresh: "Auto Refresh",
    refresh: "Refresh",
    confirm: "Confirm",
    import: "Import",
    export: "Export",
    currentEditArea: "Current Running Area",
    selectEditArea: "Current Editing Area",
    noDataToImport: "No data to import",
    noDataToConfirm: "No data to confirm",
    importSuccess: "Setting value import successful",
    importFailed: "Setting value import failed",
    updateSuccess: "Setting value update successful",
    updateFailed: "Setting value update failed",
    requestFailed: "Request failed, please try again later",
    setEditArea: "Set",
    setEditAreaTitle: "Set Editing Area",
    setEditAreaSuccess: "Editing area set successful",
    modifiedWarning: "There are unsaved changes, continue to refresh?",
    autoRefreshWarning: "There are unsaved changes, continue to enable auto refresh?",
    autoRefreshDisabled: "Auto refresh is enabled, modification is not allowed",
    invalidValue: "Setting value {name} {value} is not valid",
    exportSuccess: "Export device setting value successful",
    exportFailed: "Export device setting value failed",
    noDiffData: "No difference data obtained",
    table: {
      index: "Index",
      name: "Name",
      description: "Description",
      value: "Value",
      minValue: "Min Value",
      maxValue: "Max Value",
      step: "Step",
      address: "Address",
      unit: "Unit",
      operation: "Operation"
    },
    search: {
      namePlaceholder: "Enter setting name to search",
      descPlaceholder: "Enter setting description to search"
    }
  },
  remoteControl: {
    title: "Remote Control",
    sequence: "Index",
    shortAddress: "Short Address",
    description: "Description",
    control: "Control Split/Control Combine",
    type: "Type",
    operation: "Operation",
    directControl: "Direct Control",
    selectControl: "Select Control",
    controlClose: "Control Split",
    controlOpen: "Control Combine",
    noCheck: "No Check",
    syncCheck: "Check Sync",
    deadCheck: "Check No Pressure",
    confirmInfo: "Confirm Information",
    execute: "Execute",
    cancel: "Cancel",
    confirm: "Confirm",
    success: "Success",
    failed: "Failed",
    errorInfo: "Error Information",
    promptInfo: "Prompt Information",
    confirmSuccess: "Select successful, execute?",
    executeSuccess: "Execute successful",
    cancelSuccess: "Cancel successful",
    executeFailed: "Remote control execute failed, reason:",
    cancelFailed: "Remote control cancel failed, reason:",
    remoteExecuteSuccess: "Remote control execute successful",
    remoteCancelSuccess: "Remote control cancel successful"
  },
  remoteDrive: {
    action: "Action",
    executeSuccess: "Execute successful",
    executeFailed: "Execute failed",
    prompt: "Prompt Information",
    error: "Error Information",
    confirm: "Confirm",
    shortAddress: "Short Address",
    description: "Description",
    operation: "Operation",
    enterToFilter: "Enter short address filter",
    enterToFilterDesc: "Enter description filter",
    actionSuccess: "Action execute successful",
    actionFailed: "Action execute failed",
    failureReason: "Failure Reason",
    sequence: "Index"
  },
  remoteSignal: {
    autoRefresh: "Auto Refresh",
    refresh: "Refresh",
    export: "Export",
    sequence: "Index",
    name: "Name",
    description: "Description",
    value: "Value",
    quality: "Quality",
    searchName: "Enter name to search",
    searchDesc: "Enter description to search",
    searchValue: "Enter value to search",
    exportTitle: "Export Device Teleindication Info",
    exportSuccess: "Export device teleindication info successful",
    exportFailed: "Export device teleindication info failed",
    exportSuccessWithPath: "Export device teleindication info successful,",
    exportFailedWithError: "Export device teleindication info failed:",
    invalidData: "Invalid data:",
    errorInDataCallback: "Data callback processing error:",
    errorFetchingData: "Data fetching error:"
  },
  remoteTelemetry: {
    autoRefresh: "Auto Refresh",
    refresh: "Refresh",
    export: "Export",
    sequence: "Index",
    name: "Name",
    description: "Description",
    value: "Value",
    unit: "Unit",
    quality: "Quality",
    searchName: "Enter name to search",
    searchDesc: "Enter description to search",
    searchValue: "Enter value to search",
    exportTitle: "Export Device State Info",
    exportSuccess: "Export device state info successful",
    exportFailed: "Export device state info failed",
    exportSuccessWithPath: "Export device state info successful,",
    exportFailedWithError: "Export device state info failed:",
    confirm: "Confirm",
    tip: "Tip",
    exportFileName: "Device State Info",
    selectPathLog: "Select path:"
  },
  remote: {
    directControl: "Direct Control",
    selectControl: "Select Control",
    serialNumber: "Index",
    shortAddress: "Short Address",
    description: "Description",
    value: "Value",
    operation: "Operation",
    inputShortAddressFilter: "Enter short address filter",
    inputDescriptionFilter: "Enter description filter",
    invalidData: "Data {name} value {value} is not valid",
    error: "Error",
    success: "Success",
    executeSuccess: "Execute successful",
    prompt: "Prompt Information",
    confirm: "Confirm",
    errorInfo: "Error Information",
    executeFailed: "Remote telecontrol execute failed, reason: {msg}",
    executeSuccessLog: "{desc} Remote telecontrol execute successful",
    cancelSuccess: "Cancel successful",
    cancelFailed: "Remote telecontrol cancel failed, reason: {msg}",
    selectSuccess: "Select successful, execute?",
    confirmInfo: "Confirm Information",
    execute: "Execute",
    cancel: "Cancel"
  },
  report: {
    uploadWave: "Upload Wave",
    searchHistory: "Get History Report",
    saveResult: "Save Result",
    clearContent: "Clear Content",
    date: "Date",
    query: "Query",
    save: "Save",
    autoRefresh: "Auto Refresh",
    stopRefresh: "Stop Refresh",
    clearList: "Clear List",
    progressInfo: "Progress Information",
    loading: "Data loading",
    reportNo: "Report Index",
    time: "Time",
    description: "Description",
    noFileToUpload: "No file to upload",
    uploadSuccess: "Wave file upload complete",
    uploadPath: "Wave file upload complete, path:",
    noDataToSave: "No data to save",
    saveSuccess: "Save successful",
    saveReport: "Save Report",
    openWaveConfirm: "Open wave file with third-party tool?",
    confirm: "Confirm",
    cancel: "Cancel",
    waveToolNotConfigured: "Wave tool not configured",
    pleaseSelectTimeRange: "Please select complete time range",
    querying: "Querying",
    reportNumber: "Report Index",
    operationAddress: "Operation Address",
    operationParams: "Operation Parameters",
    result: "Result",
    progress: "Progress Information",
    loadingText: "Loading",
    selectCompleteTimeRange: "Please select complete time range",
    fileUploading: "Wave file upload in progress",
    fileUploadComplete: "File upload complete"
  },
  customMenu: {
    addMenu: "Add Custom Menu",
    editMenu: "Edit Custom Menu",
    deleteMenu: "Delete Custom Menu",
    addReport: "Add Custom Report",
    editReport: "Edit Custom Report",
    deleteReport: "Delete Custom Report",
    addPointGroup: "Add Custom Point Group",
    editPointGroup: "Edit Custom Point Group",
    selectedPoints: "Selected Points",
    selectFc: "Select FC",
    filterPlaceholder: "Filter by name/description",
    menuName: "Group Name",
    menuDesc: "Description",
    reportName: "Report Name",
    reportDesc: "Description",
    reportKeyword: "Keyword",
    reportInherit: "Inherit Report",
    inputMenuName: "Please enter group name",
    inputMenuDesc: "Please enter description",
    inputReportName: "Please enter report name",
    inputReportDesc: "Please enter description",
    inputReportKeyword: "Please enter keyword",
    selectReportInherit: "Please select inherit report",
    cancel: "Cancel",
    confirm: "Confirm",
    successAddMenu: "Custom menu added successfully",
    successEditMenu: "Custom menu edited successfully",
    successDeleteMenu: "Custom menu deleted successfully",
    successAddReport: "Custom report added successfully",
    successEditReport: "Custom report edited successfully",
    successDeleteReport: "Custom report deleted successfully",
    errorAction: "Operation failed",
    errorDelete: "Delete failed",
    confirmDeleteMenu: "Are you sure you want to delete this custom menu?",
    confirmDeleteReport: "Are you sure you want to delete this custom report?",
    tip: "Tip"
  },
  tree: {
    inputGroupName: "Please enter group name",
    expandAll: "Expand All",
    collapseAll: "Collapse All"
  }
};
